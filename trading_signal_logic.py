import pandas as pd
import numpy as np

def generate_additional_signals(df):
    """
    Generate additional buy/sell signals based on P_PCT_007 recovery logic
    
    Logic:
    - When Flag_007 is 'B' and P_PCT_007 < -1, monitor for downward movement
    - When it hits a low (e.g., -6) and starts recovering (e.g., to -4), generate buy signal
    - Same logic applies for 'S' signals
    
    Args:
        df: DataFrame with columns ['close', 'Signal_V_007', 'Flag_007', 'P_PCT_007']
    
    Returns:
        DataFrame with additional columns for new signals
    """
    
    # Create a copy to avoid modifying original
    df_result = df.copy()
    
    # Initialize new signal columns
    df_result['Additional_Signal'] = 0
    df_result['Additional_Flag'] = ''
    df_result['Recovery_Trigger'] = False
    
    # Parameters for the logic
    loss_threshold = -1.0  # Threshold below which we start monitoring
    recovery_threshold = 2.0  # Minimum recovery amount to trigger signal
    
    # Track state for each position
    monitoring_loss = False
    current_flag = None
    lowest_point = 0
    highest_point = 0
    entry_point = 0
    
    for i in range(len(df_result)):
        current_flag_007 = df_result.loc[i, 'Flag_007']
        current_pct = df_result.loc[i, 'P_PCT_007']
        
        # Check if we have a new position (Flag_007 is B or S)
        if current_flag_007 in ['B', 'S']:
            # Reset monitoring state for new position
            monitoring_loss = False
            current_flag = current_flag_007
            lowest_point = current_pct
            highest_point = current_pct
            entry_point = current_pct
            
            # Start monitoring if we're already below threshold
            if current_pct < loss_threshold:
                monitoring_loss = True
        
        # Continue monitoring if we have an active position
        elif current_flag is not None:
            # Check if we should start monitoring (crossed below threshold)
            if not monitoring_loss and current_pct < loss_threshold:
                monitoring_loss = True
                lowest_point = current_pct
                highest_point = current_pct
            
            # If we're monitoring, track the lowest/highest points
            if monitoring_loss:
                if current_flag == 'B':
                    # For buy positions, track lowest point (most negative)
                    if current_pct < lowest_point:
                        lowest_point = current_pct
                    
                    # Check for recovery (moving up from lowest point)
                    if current_pct > lowest_point + recovery_threshold:
                        df_result.loc[i, 'Additional_Signal'] = 1
                        df_result.loc[i, 'Additional_Flag'] = 'B'
                        df_result.loc[i, 'Recovery_Trigger'] = True
                        monitoring_loss = False  # Reset monitoring
                        
                elif current_flag == 'S':
                    # For sell positions, track highest point (most positive loss)
                    if current_pct > highest_point:
                        highest_point = current_pct
                    
                    # Check for recovery (moving down from highest point)
                    if current_pct < highest_point - recovery_threshold:
                        df_result.loc[i, 'Additional_Signal'] = 1
                        df_result.loc[i, 'Additional_Flag'] = 'S'
                        df_result.loc[i, 'Recovery_Trigger'] = True
                        monitoring_loss = False  # Reset monitoring
    
    return df_result

def load_and_process_data(file_path):
    """
    Load data from Excel file and process it
    """
    try:
        # Load the Excel file
        df = pd.read_excel(file_path)
        
        # Display basic info about the data
        print("Data loaded successfully!")
        print(f"Shape: {df.shape}")
        print(f"Columns: {df.columns.tolist()}")
        
        # Check if required columns exist
        required_cols = ['close', 'Signal_V_007', 'Flag_007', 'P_PCT_007']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"Warning: Missing columns: {missing_cols}")
            print("Available columns:", df.columns.tolist())
            return None
        
        # Process the data
        df_processed = generate_additional_signals(df)
        
        return df_processed
        
    except Exception as e:
        print(f"Error loading data: {e}")
        return None

def analyze_signals(df):
    """
    Analyze the generated signals
    """
    if df is None:
        return
    
    print("\n=== Signal Analysis ===")
    
    # Original signals
    original_signals = df[df['Flag_007'].isin(['B', 'S'])]
    print(f"Original signals: {len(original_signals)}")
    print(f"  - Buy signals: {len(original_signals[original_signals['Flag_007'] == 'B'])}")
    print(f"  - Sell signals: {len(original_signals[original_signals['Flag_007'] == 'S'])}")
    
    # Additional signals
    additional_signals = df[df['Additional_Signal'] == 1]
    print(f"\nAdditional recovery signals: {len(additional_signals)}")
    print(f"  - Buy signals: {len(additional_signals[additional_signals['Additional_Flag'] == 'B'])}")
    print(f"  - Sell signals: {len(additional_signals[additional_signals['Additional_Flag'] == 'S'])}")
    
    # Show some examples
    if len(additional_signals) > 0:
        print("\n=== Sample Additional Signals ===")
        cols_to_show = ['close', 'Signal_V_007', 'Flag_007', 'P_PCT_007', 'Additional_Signal', 'Additional_Flag']
        print(additional_signals[cols_to_show].head(10))

def save_results(df, output_path):
    """
    Save the processed data to Excel
    """
    try:
        df.to_excel(output_path, index=False)
        print(f"\nResults saved to: {output_path}")
    except Exception as e:
        print(f"Error saving results: {e}")

# Main execution
if __name__ == "__main__":
    # Load and process the data
    input_file = "Data.csv.xlsx"
    output_file = "Data_with_additional_signals.xlsx"
    
    print("Loading and processing trading data...")
    df_processed = load_and_process_data(input_file)
    
    if df_processed is not None:
        # Analyze the results
        analyze_signals(df_processed)
        
        # Save the results
        save_results(df_processed, output_file)
        
        print("\nProcessing completed!")
    else:
        print("Failed to process data. Please check the file and column names.")
