"""
Analyze and visualize the recovery signal results
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

def load_and_analyze_results():
    """
    Load both result files and compare them
    """
    try:
        # Load original data
        df_original = pd.read_excel("Data.csv.xlsx")
        df_original = df_original.rename(columns={'CLOSE': 'close', 'TIME': 'time'})
        
        # Load results
        df_standard = pd.read_excel("Data_with_recovery_signals.xlsx")
        df_sensitive = pd.read_excel("Data_with_sensitive_signals.xlsx")
        
        print("=== RECOVERY SIGNAL ANALYSIS SUMMARY ===")
        print(f"Total data points: {len(df_original)}")
        print(f"Original signals: {len(df_original[df_original['Flag_007'].isin(['B', 'S'])])}")
        
        # Standard parameters results
        standard_recovery = df_standard[df_standard['Recovery_Signal'] == 1]
        print(f"\nStandard Parameters (loss_threshold=-1.0, recovery_threshold=2.0, min_loss_depth=3.0):")
        print(f"  Recovery signals generated: {len(standard_recovery)}")
        print(f"  Buy recoveries: {len(standard_recovery[standard_recovery['Recovery_Flag'] == 'B'])}")
        print(f"  Sell recoveries: {len(standard_recovery[standard_recovery['Recovery_Flag'] == 'S'])}")
        
        # Sensitive parameters results
        sensitive_recovery = df_sensitive[df_sensitive['Recovery_Signal'] == 1]
        print(f"\nSensitive Parameters (loss_threshold=-0.5, recovery_threshold=1.0, min_loss_depth=1.0):")
        print(f"  Recovery signals generated: {len(sensitive_recovery)}")
        print(f"  Buy recoveries: {len(sensitive_recovery[sensitive_recovery['Recovery_Flag'] == 'B'])}")
        print(f"  Sell recoveries: {len(sensitive_recovery[sensitive_recovery['Recovery_Flag'] == 'S'])}")
        
        return df_original, df_standard, df_sensitive, standard_recovery, sensitive_recovery
        
    except Exception as e:
        print(f"Error loading results: {e}")
        return None, None, None, None, None

def plot_recovery_signals(df_original, df_with_signals, recovery_signals, title="Recovery Signals"):
    """
    Plot the recovery signals on price and P_PCT_007 charts
    """
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10), sharex=True)
    
    # Plot 1: Price with signals
    ax1.plot(range(len(df_original)), df_original['close'], label='Close Price', color='black', linewidth=1)
    
    # Original signals
    buy_signals = df_original[df_original['Flag_007'] == 'B']
    sell_signals = df_original[df_original['Flag_007'] == 'S']
    
    if len(buy_signals) > 0:
        ax1.scatter(buy_signals.index, buy_signals['close'], color='green', marker='^', s=50, label='Original Buy', alpha=0.6)
    if len(sell_signals) > 0:
        ax1.scatter(sell_signals.index, sell_signals['close'], color='red', marker='v', s=50, label='Original Sell', alpha=0.6)
    
    # Recovery signals
    if len(recovery_signals) > 0:
        recovery_buy = recovery_signals[recovery_signals['Recovery_Flag'] == 'B']
        recovery_sell = recovery_signals[recovery_signals['Recovery_Flag'] == 'S']
        
        if len(recovery_buy) > 0:
            ax1.scatter(recovery_buy.index, recovery_buy['close'], color='lightgreen', marker='^', s=150, 
                       label='Recovery Buy', zorder=5, edgecolor='darkgreen', linewidth=2)
        if len(recovery_sell) > 0:
            ax1.scatter(recovery_sell.index, recovery_sell['close'], color='lightcoral', marker='v', s=150, 
                       label='Recovery Sell', zorder=5, edgecolor='darkred', linewidth=2)
    
    ax1.set_ylabel('Price')
    ax1.set_title(f'{title} - Price Chart')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: P_PCT_007 with recovery signals
    ax2.plot(range(len(df_original)), df_original['P_PCT_007'], label='P_PCT_007 (ROE)', color='blue', linewidth=1)
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5, label='Break-even')
    ax2.axhline(y=-1, color='red', linestyle='--', alpha=0.7, label='Standard Loss Threshold (-1%)')
    ax2.axhline(y=-0.5, color='orange', linestyle='--', alpha=0.7, label='Sensitive Loss Threshold (-0.5%)')
    
    # Recovery signals on P_PCT_007
    if len(recovery_signals) > 0:
        recovery_buy = recovery_signals[recovery_signals['Recovery_Flag'] == 'B']
        recovery_sell = recovery_signals[recovery_signals['Recovery_Flag'] == 'S']
        
        if len(recovery_buy) > 0:
            ax2.scatter(recovery_buy.index, recovery_buy['P_PCT_007'], color='lightgreen', marker='^', s=150, 
                       zorder=5, edgecolor='darkgreen', linewidth=2)
        if len(recovery_sell) > 0:
            ax2.scatter(recovery_sell.index, recovery_sell['P_PCT_007'], color='lightcoral', marker='v', s=150, 
                       zorder=5, edgecolor='darkred', linewidth=2)
    
    ax2.set_ylabel('P_PCT_007 (%)')
    ax2.set_xlabel('Data Point Index')
    ax2.set_title(f'{title} - ROE Chart')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'{title.lower().replace(" ", "_")}_chart.png', dpi=300, bbox_inches='tight')
    plt.show()

def detailed_signal_analysis(recovery_signals, title="Recovery Signals"):
    """
    Provide detailed analysis of recovery signals
    """
    if len(recovery_signals) == 0:
        print(f"\nNo {title.lower()} found.")
        return
    
    print(f"\n=== DETAILED {title.upper()} ANALYSIS ===")
    
    for idx, row in recovery_signals.iterrows():
        print(f"\nSignal #{idx}:")
        print(f"  Type: {row['Recovery_Flag']} (Recovery)")
        print(f"  Price: {row['close']:.4f}")
        print(f"  P_PCT_007: {row['P_PCT_007']:.2f}%")
        print(f"  Loss Depth: {row['Loss_Depth']:.2f}%")
        print(f"  Recovery Amount: {row['Recovery_Amount']:.2f}%")
        print(f"  Reason: {row['Signal_Reason']}")

def compare_parameters():
    """
    Compare the effectiveness of different parameter settings
    """
    print("\n=== PARAMETER COMPARISON ===")
    print("Standard Parameters:")
    print("  - Loss Threshold: -1.0% (more conservative)")
    print("  - Recovery Threshold: 2.0% (requires larger recovery)")
    print("  - Min Loss Depth: 3.0% (requires deeper loss)")
    print("  - Result: Fewer but higher-quality signals")
    
    print("\nSensitive Parameters:")
    print("  - Loss Threshold: -0.5% (more sensitive)")
    print("  - Recovery Threshold: 1.0% (smaller recovery needed)")
    print("  - Min Loss Depth: 1.0% (less loss required)")
    print("  - Result: More signals but potentially more noise")
    
    print("\nRecommendation:")
    print("  - Use Standard for conservative trading")
    print("  - Use Sensitive for more active trading")
    print("  - Consider your risk tolerance and trading style")

def main():
    """
    Main analysis function
    """
    # Load and analyze results
    df_original, df_standard, df_sensitive, standard_recovery, sensitive_recovery = load_and_analyze_results()
    
    if df_original is None:
        return
    
    # Plot standard parameters results
    if len(standard_recovery) > 0:
        plot_recovery_signals(df_original, df_standard, standard_recovery, "Standard Parameters")
        detailed_signal_analysis(standard_recovery, "Standard Recovery Signals")
    
    # Plot sensitive parameters results
    if len(sensitive_recovery) > 0:
        plot_recovery_signals(df_original, df_sensitive, sensitive_recovery, "Sensitive Parameters")
        detailed_signal_analysis(sensitive_recovery, "Sensitive Recovery Signals")
    
    # Compare parameters
    compare_parameters()
    
    print("\n=== NEXT STEPS ===")
    print("1. Review the generated charts to understand signal timing")
    print("2. Check the Excel files for detailed signal data")
    print("3. Adjust parameters based on your trading preferences")
    print("4. Consider backtesting the signals on historical data")

if __name__ == "__main__":
    main()
