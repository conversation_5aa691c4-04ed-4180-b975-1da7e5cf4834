import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

class TradingSignalGenerator:
    def __init__(self, loss_threshold=-1.0, recovery_threshold=2.0, min_loss_depth=3.0):
        """
        Initialize the signal generator with customizable parameters
        
        Args:
            loss_threshold: P_PCT_007 threshold below which we start monitoring (default: -1.0)
            recovery_threshold: Minimum recovery amount to trigger signal (default: 2.0)
            min_loss_depth: Minimum depth of loss before considering recovery (default: 3.0)
        """
        self.loss_threshold = loss_threshold
        self.recovery_threshold = recovery_threshold
        self.min_loss_depth = min_loss_depth
        
    def generate_recovery_signals(self, df):
        """
        Generate recovery signals based on P_PCT_007 movement
        """
        df_result = df.copy()
        
        # Initialize new columns
        df_result['Recovery_Signal'] = 0
        df_result['Recovery_Flag'] = ''
        df_result['Loss_Depth'] = 0.0
        df_result['Recovery_Amount'] = 0.0
        df_result['Signal_Reason'] = ''
        
        # Track positions
        current_positions = {}  # Track multiple positions if needed
        position_id = 0
        
        for i in range(len(df_result)):
            current_flag = df_result.loc[i, 'Flag_007']
            current_pct = df_result.loc[i, 'P_PCT_007']
            
            # Handle new position signals
            if current_flag in ['B', 'S']:
                position_id += 1
                current_positions[position_id] = {
                    'flag': current_flag,
                    'entry_pct': current_pct,
                    'monitoring': False,
                    'lowest_point': current_pct,
                    'highest_point': current_pct,
                    'max_loss_depth': 0
                }
                
                # Start monitoring if already below threshold
                if current_pct < self.loss_threshold:
                    current_positions[position_id]['monitoring'] = True
            
            # Process existing positions
            positions_to_remove = []
            for pos_id, position in current_positions.items():
                
                # Start monitoring if crossed threshold
                if not position['monitoring'] and current_pct < self.loss_threshold:
                    position['monitoring'] = True
                    position['lowest_point'] = current_pct
                    position['highest_point'] = current_pct
                
                # If monitoring, update tracking
                if position['monitoring']:
                    if position['flag'] == 'B':
                        # Track lowest point for buy positions
                        if current_pct < position['lowest_point']:
                            position['lowest_point'] = current_pct
                        
                        # Calculate loss depth
                        loss_depth = abs(position['lowest_point'] - position['entry_pct'])
                        position['max_loss_depth'] = max(position['max_loss_depth'], loss_depth)
                        
                        # Check for recovery
                        recovery = current_pct - position['lowest_point']
                        if (loss_depth >= self.min_loss_depth and 
                            recovery >= self.recovery_threshold):
                            
                            df_result.loc[i, 'Recovery_Signal'] = 1
                            df_result.loc[i, 'Recovery_Flag'] = 'B'
                            df_result.loc[i, 'Loss_Depth'] = loss_depth
                            df_result.loc[i, 'Recovery_Amount'] = recovery
                            df_result.loc[i, 'Signal_Reason'] = f'Buy recovery: {recovery:.2f} from {position["lowest_point"]:.2f}'
                            
                            positions_to_remove.append(pos_id)
                    
                    elif position['flag'] == 'S':
                        # Track highest point for sell positions
                        if current_pct > position['highest_point']:
                            position['highest_point'] = current_pct
                        
                        # Calculate loss depth
                        loss_depth = abs(position['highest_point'] - position['entry_pct'])
                        position['max_loss_depth'] = max(position['max_loss_depth'], loss_depth)
                        
                        # Check for recovery
                        recovery = position['highest_point'] - current_pct
                        if (loss_depth >= self.min_loss_depth and 
                            recovery >= self.recovery_threshold):
                            
                            df_result.loc[i, 'Recovery_Signal'] = 1
                            df_result.loc[i, 'Recovery_Flag'] = 'S'
                            df_result.loc[i, 'Loss_Depth'] = loss_depth
                            df_result.loc[i, 'Recovery_Amount'] = recovery
                            df_result.loc[i, 'Signal_Reason'] = f'Sell recovery: {recovery:.2f} from {position["highest_point"]:.2f}'
                            
                            positions_to_remove.append(pos_id)
            
            # Remove completed positions
            for pos_id in positions_to_remove:
                del current_positions[pos_id]
        
        return df_result
    
    def analyze_performance(self, df):
        """
        Analyze the performance of the recovery signals
        """
        print("=== Recovery Signal Analysis ===")
        
        # Original signals
        original_signals = df[df['Flag_007'].isin(['B', 'S'])]
        recovery_signals = df[df['Recovery_Signal'] == 1]
        
        print(f"Original signals: {len(original_signals)}")
        print(f"Recovery signals generated: {len(recovery_signals)}")
        
        if len(recovery_signals) > 0:
            print(f"\nRecovery signal breakdown:")
            print(f"  - Buy recoveries: {len(recovery_signals[recovery_signals['Recovery_Flag'] == 'B'])}")
            print(f"  - Sell recoveries: {len(recovery_signals[recovery_signals['Recovery_Flag'] == 'S'])}")
            
            print(f"\nAverage loss depth: {recovery_signals['Loss_Depth'].mean():.2f}")
            print(f"Average recovery amount: {recovery_signals['Recovery_Amount'].mean():.2f}")
            
            print(f"\nLoss depth range: {recovery_signals['Loss_Depth'].min():.2f} to {recovery_signals['Loss_Depth'].max():.2f}")
            print(f"Recovery amount range: {recovery_signals['Recovery_Amount'].min():.2f} to {recovery_signals['Recovery_Amount'].max():.2f}")
        
        return recovery_signals
    
    def plot_signals(self, df, start_idx=0, end_idx=None):
        """
        Plot the price and signals for visualization
        """
        if end_idx is None:
            end_idx = len(df)
        
        df_plot = df.iloc[start_idx:end_idx].copy()
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10), sharex=True)
        
        # Plot price
        ax1.plot(df_plot.index, df_plot['close'], label='Close Price', color='black', linewidth=1)
        
        # Plot original signals
        buy_signals = df_plot[df_plot['Flag_007'] == 'B']
        sell_signals = df_plot[df_plot['Flag_007'] == 'S']
        
        ax1.scatter(buy_signals.index, buy_signals['close'], color='green', marker='^', s=100, label='Original Buy', zorder=5)
        ax1.scatter(sell_signals.index, sell_signals['close'], color='red', marker='v', s=100, label='Original Sell', zorder=5)
        
        # Plot recovery signals
        recovery_buy = df_plot[df_plot['Recovery_Flag'] == 'B']
        recovery_sell = df_plot[df_plot['Recovery_Flag'] == 'S']
        
        ax1.scatter(recovery_buy.index, recovery_buy['close'], color='lightgreen', marker='^', s=150, label='Recovery Buy', zorder=6, edgecolor='darkgreen')
        ax1.scatter(recovery_sell.index, recovery_sell['close'], color='lightcoral', marker='v', s=150, label='Recovery Sell', zorder=6, edgecolor='darkred')
        
        ax1.set_ylabel('Price')
        ax1.set_title('Price and Trading Signals')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Plot P_PCT_007
        ax2.plot(df_plot.index, df_plot['P_PCT_007'], label='P_PCT_007 (ROE)', color='blue', linewidth=1)
        ax2.axhline(y=self.loss_threshold, color='red', linestyle='--', alpha=0.7, label=f'Loss Threshold ({self.loss_threshold})')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # Highlight recovery signals
        ax2.scatter(recovery_buy.index, recovery_buy['P_PCT_007'], color='lightgreen', marker='^', s=150, zorder=6, edgecolor='darkgreen')
        ax2.scatter(recovery_sell.index, recovery_sell['P_PCT_007'], color='lightcoral', marker='v', s=150, zorder=6, edgecolor='darkred')
        
        ax2.set_ylabel('P_PCT_007 (%)')
        ax2.set_xlabel('Index')
        ax2.set_title('ROE and Recovery Signals')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()

def main():
    """
    Main function to run the signal generation
    """
    # Initialize the signal generator with custom parameters
    # You can adjust these parameters based on your requirements
    signal_gen = TradingSignalGenerator(
        loss_threshold=-1.0,    # Start monitoring when P_PCT_007 < -1%
        recovery_threshold=2.0,  # Trigger signal when recovery >= 2%
        min_loss_depth=3.0      # Minimum loss depth of 3% before considering recovery
    )
    
    try:
        # Load data
        print("Loading data from Data.csv.xlsx...")
        df = pd.read_excel("Data.csv.xlsx")
        
        print(f"Data loaded: {df.shape[0]} rows, {df.shape[1]} columns")
        print(f"Columns: {df.columns.tolist()}")
        
        # Generate recovery signals
        print("\nGenerating recovery signals...")
        df_with_signals = signal_gen.generate_recovery_signals(df)
        
        # Analyze results
        recovery_signals = signal_gen.analyze_performance(df_with_signals)
        
        # Save results
        output_file = "Data_with_recovery_signals.xlsx"
        df_with_signals.to_excel(output_file, index=False)
        print(f"\nResults saved to: {output_file}")
        
        # Show sample recovery signals
        if len(recovery_signals) > 0:
            print("\n=== Sample Recovery Signals ===")
            cols_to_show = ['close', 'Flag_007', 'P_PCT_007', 'Recovery_Signal', 'Recovery_Flag', 'Loss_Depth', 'Recovery_Amount', 'Signal_Reason']
            available_cols = [col for col in cols_to_show if col in df_with_signals.columns]
            print(recovery_signals[available_cols].head(10).to_string())
        
        return df_with_signals
        
    except Exception as e:
        print(f"Error: {e}")
        return None

if __name__ == "__main__":
    df_result = main()
