"""
Simple script to run the trading signal analysis on your data
"""

import pandas as pd
import sys
from advanced_signal_generator import TradingSignalGenerator

def quick_test():
    """
    Quick test of the signal generation logic
    """
    try:
        # Load the data
        print("Loading Data.csv.xlsx...")
        df = pd.read_excel("Data.csv.xlsx")
        
        print(f"Data shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        
        # Map actual column names to expected names
        column_mapping = {
            'CLOSE': 'close',
            'TIME': 'time'
        }

        # Rename columns to match expected names
        df = df.rename(columns=column_mapping)

        # Check if required columns exist
        required_cols = ['close', 'Signal_V_007', 'Flag_007', 'P_PCT_007']
        missing_cols = [col for col in required_cols if col not in df.columns]

        if missing_cols:
            print(f"\nMissing required columns: {missing_cols}")
            print("Available columns:", list(df.columns))
            print("Please ensure your Excel file has these columns:")
            for col in required_cols:
                print(f"  - {col}")
            return None
        
        # Show sample data
        print("\n=== Sample Data ===")
        print(df[required_cols].head(10))
        
        # Check data types and basic stats
        print("\n=== Data Info ===")
        print(f"P_PCT_007 range: {df['P_PCT_007'].min():.2f} to {df['P_PCT_007'].max():.2f}")
        print(f"Flag_007 values: {df['Flag_007'].value_counts().to_dict()}")
        
        # Initialize signal generator
        signal_gen = TradingSignalGenerator(
            loss_threshold=-1.0,     # Monitor when ROE < -1%
            recovery_threshold=2.0,   # Signal when recovery >= 2%
            min_loss_depth=3.0       # Minimum 3% loss before recovery
        )
        
        # Generate signals
        print("\n=== Generating Recovery Signals ===")
        df_with_signals = signal_gen.generate_recovery_signals(df)
        
        # Analyze results
        recovery_signals = signal_gen.analyze_performance(df_with_signals)
        
        # Save results
        output_file = "Data_with_recovery_signals.xlsx"
        df_with_signals.to_excel(output_file, index=False)
        print(f"\nResults saved to: {output_file}")
        
        # Show detailed results if any recovery signals were generated
        if len(recovery_signals) > 0:
            print("\n=== Recovery Signal Details ===")
            for idx, row in recovery_signals.iterrows():
                print(f"Row {idx}: {row['Recovery_Flag']} signal - {row['Signal_Reason']}")
        else:
            print("\nNo recovery signals generated with current parameters.")
            print("You might want to adjust the parameters:")
            print("  - Lower loss_threshold (e.g., -0.5)")
            print("  - Lower recovery_threshold (e.g., 1.0)")
            print("  - Lower min_loss_depth (e.g., 1.0)")
        
        return df_with_signals
        
    except FileNotFoundError:
        print("Error: Data.csv.xlsx not found in current directory")
        print("Please make sure the file exists and has the correct name")
        return None
    except Exception as e:
        print(f"Error: {e}")
        return None

def test_with_custom_parameters():
    """
    Test with more sensitive parameters to catch more signals
    """
    print("\n" + "="*50)
    print("TESTING WITH MORE SENSITIVE PARAMETERS")
    print("="*50)
    
    try:
        df = pd.read_excel("Data.csv.xlsx")

        # Map actual column names to expected names
        column_mapping = {
            'CLOSE': 'close',
            'TIME': 'time'
        }

        # Rename columns to match expected names
        df = df.rename(columns=column_mapping)
        
        # More sensitive parameters
        signal_gen = TradingSignalGenerator(
            loss_threshold=-0.5,     # Monitor when ROE < -0.5%
            recovery_threshold=1.0,   # Signal when recovery >= 1%
            min_loss_depth=1.0       # Minimum 1% loss before recovery
        )
        
        df_with_signals = signal_gen.generate_recovery_signals(df)
        recovery_signals = signal_gen.analyze_performance(df_with_signals)
        
        # Save with different name
        output_file = "Data_with_sensitive_signals.xlsx"
        df_with_signals.to_excel(output_file, index=False)
        print(f"Sensitive analysis saved to: {output_file}")
        
        return df_with_signals
        
    except Exception as e:
        print(f"Error in sensitive test: {e}")
        return None

if __name__ == "__main__":
    print("=== Trading Signal Recovery Analysis ===")
    
    # Run the main test
    result1 = quick_test()
    
    # Run with more sensitive parameters
    result2 = test_with_custom_parameters()
    
    print("\n=== Analysis Complete ===")
    print("Check the generated Excel files for results:")
    print("  - Data_with_recovery_signals.xlsx (standard parameters)")
    print("  - Data_with_sensitive_signals.xlsx (sensitive parameters)")
